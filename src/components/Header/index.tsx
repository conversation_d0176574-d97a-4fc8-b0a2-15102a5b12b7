"use client";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import ThemeToggler from "./ThemeToggler";
import LanguageSwitcher from "./LanguageSwitcher";
import LoginModal from "@/components/Auth/LoginModal";
import { useI18n } from "@/i18n/context";
import { Menu } from "@/types/menu";
import { AuthService, User } from "@/services/auth";

const Header = () => {
  const { t } = useI18n();

  // Authentication state
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check authentication status on component mount
  useEffect(() => {
    const checkAuth = () => {
      const authenticated = AuthService.isAuthenticated();
      const userData = AuthService.getUser();
      setIsAuthenticated(authenticated);
      setUser(userData);
    };

    checkAuth();
    // Listen for storage changes (login/logout in other tabs)
    window.addEventListener('storage', checkAuth);
    return () => window.removeEventListener('storage', checkAuth);
  }, []);

  // Handle login success
  const handleLoginSuccess = () => {
    const userData = AuthService.getUser();
    setUser(userData);
    setIsAuthenticated(true);
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await AuthService.logout();
      setUser(null);
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Dynamic menu data with translations
  const getMenuData = (): Menu[] => [
    {
      id: 1,
      title: t('header.home'),
      path: "/",
      newTab: false,
    },
    {
      id: 2,
      title: t('header.about'),
      path: "/about",
      newTab: false,
    },
    {
      id: 3,
      title: t('header.contact'),
      path: "/contact",
      newTab: false,
    },
    {
      id: 4,
      title: t('header.services'),
      newTab: false,
      submenu: [
        {
          id: 31,
          title: t('header.submenu.powerpoint'),
          path: "/services/powerpoint",
          newTab: false,
        },
        {
          id: 32,
          title: t('header.submenu.document'),
          path: "/services/document",
          newTab: false,
        },
        {
          id: 33,
          title: t('header.submenu.cv'),
          path: "/services/cv",
          newTab: false,
        },
        {
          id: 34,
          title: t('header.submenu.allServices'),
          path: "/about",
          newTab: false,
        },
      ],
    }
  ];

  // Navbar toggle
  const [navbarOpen, setNavbarOpen] = useState(false);
  const navbarToggleHandler = () => {
    setNavbarOpen(!navbarOpen);
  };

  // Sticky Navbar
  const [sticky, setSticky] = useState(false);
  const handleStickyNavbar = () => {
    if (window.scrollY >= 80) {
      setSticky(true);
    } else {
      setSticky(false);
    }
  };
  useEffect(() => {
    window.addEventListener("scroll", handleStickyNavbar);
  });

  // submenu handler
  const [openIndex, setOpenIndex] = useState(-1);
  const handleSubmenu = (index) => {
    if (openIndex === index) {
      setOpenIndex(-1);
    } else {
      setOpenIndex(index);
    }
  };

  const usePathName = usePathname();

  return (
    <>
      <header
        className={`header top-0 left-0 z-40 flex w-full items-center ${
          sticky
            ? "dark:bg-gray-dark dark:shadow-sticky-dark shadow-sticky fixed z-9999 bg-white/95 backdrop-blur-sm transition-all duration-300"
            : "absolute bg-transparent"
        }`}
      >
        <div className="container">
          <div className="flex items-center justify-between">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link
                href="/"
                className={`header-logo flex items-center space-x-2 sm:space-x-3 ${
                  sticky ? "py-3 sm:py-4 lg:py-2" : "py-6 sm:py-8"
                } `}
              >
                <Image
                  src="/images/logo.png"
                  alt="Sademy Logo"
                  width={40}
                  height={40}
                  className="h-8 w-8 sm:h-10 sm:w-10 lg:h-12 lg:w-12 h-auto w-auto"
                  priority
                />
                <span className="text-lg sm:text-xl lg:text-2xl font-bold text-black dark:text-white transition-colors duration-300">
                  {t('header.brand')}
                </span>
              </Link>
            </div>
            
            {/* Navigation menu - centered */}
            <div className="hidden lg:block mx-auto">
              <nav id="navbarCollapse" className="navbar">
                <ul className="flex space-x-12 justify-center">
                  {getMenuData().map((menuItem, index) => (
                    <li key={index} className="group relative">
                      {menuItem.path ? (
                        <Link
                          href={menuItem.path}
                          onClick={() => setNavbarOpen(false)}
                          className={`flex py-3 px-2 text-base lg:mr-0 lg:inline-flex lg:px-0 lg:py-6 rounded-md lg:rounded-none transition-all duration-300 mobile-touch-button ${
                            usePathName === menuItem.path
                              ? "text-primary dark:text-white bg-primary/10 lg:bg-transparent"
                              : "text-dark hover:text-primary dark:text-white/70 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800 lg:hover:bg-transparent"
                          }`}
                        >
                          {menuItem.title}
                        </Link>
                      ) : (
                        <>
                          <p
                            onClick={() => handleSubmenu(index)}
                            className="text-dark group-hover:text-primary flex cursor-pointer items-center justify-between py-2 text-base lg:mr-0 lg:inline-flex lg:px-0 lg:py-6 dark:text-white/70 dark:group-hover:text-white"
                          >
                            {menuItem.title}
                            <span className="pl-3">
                              <svg width="25" height="24" viewBox="0 0 25 24">
                                <path
                                  fillRule="evenodd"
                                  clipRule="evenodd"
                                  d="M6.29289 8.8427C6.68342 8.45217 7.31658 8.45217 7.70711 8.8427L12 13.1356L16.2929 8.8427C16.6834 8.45217 17.3166 8.45217 17.7071 8.8427C18.0976 9.23322 18.0976 9.86639 17.7071 10.2569L12 15.964L6.29289 10.2569C5.90237 9.86639 5.90237 9.23322 6.29289 8.8427Z"
                                  fill="currentColor"
                                />
                              </svg>
                            </span>
                          </p>
                          <div
                            className={`submenu dark:bg-dark relative top-full left-0 rounded-sm bg-white transition-[top] duration-300 group-hover:opacity-100 lg:invisible lg:absolute lg:top-[110%] lg:block lg:w-[250px] lg:p-4 lg:opacity-0 lg:shadow-lg lg:group-hover:visible lg:group-hover:top-full ${
                              openIndex === index ? "block" : "hidden"
                            }`}
                          >
                            {menuItem.submenu.map((submenuItem, index) => (
                              <Link
                                href={submenuItem.path}
                                key={index}
                                className="text-dark hover:text-primary block rounded-sm py-2.5 text-sm lg:px-3 dark:text-white/70 dark:hover:text-white"
                              >
                                {submenuItem.title}
                              </Link>
                            ))}
                          </div>
                        </>
                      )}
                    </li>
                  ))}
                </ul>
              </nav>
            </div>
            
            {/* Controls container - left side */}
            <div className="flex items-center space-x-4 sm:space-x-6">
              {/* Authentication section */}
              <div className="order-1 hidden lg:block">
                {isAuthenticated && user ? (
                  <div className="relative group">
                    <button className="flex items-center space-x-2 rounded-full bg-gray-2 dark:bg-dark-bg px-3 py-2 text-black dark:text-white hover:bg-gray-3 dark:hover:bg-gray-dark transition-colors duration-300">
                      <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                        {user.firstName.charAt(0).toUpperCase()}
                      </div>
                      <span className="text-sm font-medium hidden sm:block">{user.firstName} {user.lastName}</span>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>

                    {/* User dropdown menu */}
                    <div className="absolute top-full right-0 mt-2 w-48 bg-white dark:bg-gray-dark rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                      <div className="py-2">
                        {user.user_type === 'admin' && (
                          <Link
                            href="/admin/dashboard"
                            className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                          >
                            🏠 Admin Panel
                          </Link>
                        )}
                        {user.user_type === 'collaborator' && (
                          <Link
                            href="/collaborator/dashboard"
                            className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                          >
                            🤝 Collaborator Dashboard
                          </Link>
                        )}
                        {user.user_type === 'user' && (
                          <Link
                            href="/my-presentations"
                            className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                          >
                            📊 My Presentations
                          </Link>
                        )}
                        <button
                          onClick={handleLogout}
                          className="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                        >
                          🚪 {t('auth.logout')}
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <button
                    onClick={() => setIsLoginModalOpen(true)}
                    className="bg-primary hover:bg-primary/90 text-white font-medium px-4 py-2 rounded-md transition-colors duration-200 text-sm"
                  >
                    {t('auth.login.title')}
                  </button>
                )}
              </div>

              {/* Language switcher */}
              <div className="order-2">
                <LanguageSwitcher />
              </div>

              {/* Theme toggler */}
              <div className="order-3">
                <ThemeToggler />
              </div>
              
              {/* Mobile menu button */}
              <div className="lg:hidden order-3">
                <button
                  onClick={navbarToggleHandler}
                  id="navbarToggler"
                  aria-label="Mobile Menu"
                  className="ring-primary rounded-lg px-3 py-3 focus:ring-2 mobile-touch-button"
                >
                  <span
                    className={`relative my-1.5 block h-0.5 w-[28px] sm:w-[30px] bg-black transition-all duration-300 dark:bg-white ${
                      navbarOpen ? "top-[7px] rotate-45" : " "
                    }`}
                  />
                  <span
                    className={`relative my-1.5 block h-0.5 w-[28px] sm:w-[30px] bg-black transition-all duration-300 dark:bg-white ${
                      navbarOpen ? "opacity-0" : " "
                    }`}
                  />
                  <span
                    className={`relative my-1.5 block h-0.5 w-[28px] sm:w-[30px] bg-black transition-all duration-300 dark:bg-white ${
                      navbarOpen ? "top-[-8px] -rotate-45" : " "
                    }`}
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Mobile Navigation Menu */}
        <div className={`w-full px-4 lg:hidden absolute top-full left-0 z-30 ${navbarOpen ? "block" : "hidden"}`}>
          <nav
            className={`navbar border-body-color/50 dark:border-body-color/20 dark:bg-dark w-full rounded border-[.5px] bg-white px-6 py-6 shadow-lg`}
          >
            <ul className="block space-y-2">
              {getMenuData().map((menuItem, index) => (
                <li key={index} className="group relative">
                  {menuItem.path ? (
                    <Link
                      href={menuItem.path}
                      onClick={() => setNavbarOpen(false)}
                      className={`flex py-3 px-2 text-base rounded-md transition-all duration-300 mobile-touch-button ${
                        usePathName === menuItem.path
                          ? "text-primary dark:text-white bg-primary/10"
                          : "text-dark hover:text-primary dark:text-white/70 dark:hover:text-white hover:bg-gray-50 dark:hover:bg-gray-800"
                      }`}
                    >
                      {menuItem.title}
                    </Link>
                  ) : (
                    <>
                      <p
                        onClick={() => handleSubmenu(index)}
                        className="text-dark group-hover:text-primary flex cursor-pointer items-center justify-between py-2 text-base dark:text-white/70 dark:group-hover:text-white"
                      >
                        {menuItem.title}
                        <span className="pl-3">
                          <svg width="25" height="24" viewBox="0 0 25 24">
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M6.29289 8.8427C6.68342 8.45217 7.31658 8.45217 7.70711 8.8427L12 13.1356L16.2929 8.8427C16.6834 8.45217 17.3166 8.45217 17.7071 8.8427C18.0976 9.23322 18.0976 9.86639 17.7071 10.2569L12 15.964L6.29289 10.2569C5.90237 9.86639 5.90237 9.23322 6.29289 8.8427Z"
                              fill="currentColor"
                            />
                          </svg>
                        </span>
                      </p>
                      <div
                        className={`submenu dark:bg-dark relative rounded-sm bg-white transition-[top] duration-300 ${
                          openIndex === index ? "block" : "hidden"
                        }`}
                      >
                        {menuItem.submenu.map((submenuItem, index) => (
                          <Link
                            href={submenuItem.path}
                            key={index}
                            className="text-dark hover:text-primary block rounded-sm py-2.5 text-sm px-3 dark:text-white/70 dark:hover:text-white"
                          >
                            {submenuItem.title}
                          </Link>
                        ))}
                      </div>
                    </>
                  )}
                </li>
              ))}

              {/* Mobile Authentication */}
              <li className="border-t border-gray-200 dark:border-gray-600 pt-4 mt-4">
                {isAuthenticated && user ? (
                  <div className="space-y-2">
                    <div className="flex items-center space-x-3 px-2 py-3">
                      <div className="w-10 h-10 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
                        {user.firstName.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-dark dark:text-white">{user.firstName} {user.lastName}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">{user.phone}</p>
                      </div>
                    </div>
                    {user.user_type === 'admin' && (
                      <Link
                        href="/admin/dashboard"
                        onClick={() => setNavbarOpen(false)}
                        className="flex items-center space-x-2 px-2 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors duration-200"
                      >
                        <span>🏠</span>
                        <span>Admin Panel</span>
                      </Link>
                    )}
                    {user.user_type === 'collaborator' && (
                      <Link
                        href="/collaborator/dashboard"
                        onClick={() => setNavbarOpen(false)}
                        className="flex items-center space-x-2 px-2 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors duration-200"
                      >
                        <span>🤝</span>
                        <span>Collaborator Dashboard</span>
                      </Link>
                    )}
                    {user.user_type === 'user' && (
                      <Link
                        href="/my-presentations"
                        onClick={() => setNavbarOpen(false)}
                        className="flex items-center space-x-2 px-2 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors duration-200"
                      >
                        <span>📊</span>
                        <span>My Presentations</span>
                      </Link>
                    )}
                    <button
                      onClick={() => {
                        handleLogout();
                        setNavbarOpen(false);
                      }}
                      className="w-full flex items-center space-x-2 px-2 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors duration-200"
                    >
                      <span>🚪</span>
                      <span>{t('auth.logout')}</span>
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={() => {
                      setIsLoginModalOpen(true);
                      setNavbarOpen(false);
                    }}
                    className="w-full bg-primary hover:bg-primary/90 text-white font-medium px-4 py-3 rounded-md transition-colors duration-200 text-sm"
                  >
                    {t('auth.login.title')}
                  </button>
                )}
              </li>
            </ul>
          </nav>
        </div>

        {/* Login Modal */}
        <LoginModal
          isOpen={isLoginModalOpen}
          onClose={() => setIsLoginModalOpen(false)}
          onLoginSuccess={handleLoginSuccess}
        />
      </header>
    </>
  );
};

export default Header;
