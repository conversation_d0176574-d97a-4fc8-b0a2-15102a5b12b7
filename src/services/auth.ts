import api from './api';

export interface LoginRequest {
  phone: string;
  password: string;
}

export interface User {
  id: number;
  firstName: string;
  lastName: string;
  phone: string;
  user_type: string;
  date_joined: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  user: User;
  message: string;
}

export class AuthService {
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await api.post<LoginResponse>('/auth/login/', credentials);

      // Store tokens and user data in localStorage
      localStorage.setItem('access_token', response.data.access_token);
      localStorage.setItem('refresh_token', response.data.refresh_token);
      localStorage.setItem('user_data', JSON.stringify(response.data.user));

      return response.data;
    } catch (error: any) {
      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        throw new Error('Impossible de se connecter au serveur. Vérifiez que l\'API est démarrée.');
      }
      throw new Error(error.response?.data?.message || 'Échec de la connexion');
    }
  }

  static async logout(): Promise<void> {
    try {
      await api.post('/auth/logout/');
    } catch (error) {
      // Even if logout fails on server, clear local storage
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user_data');
    }
  }

  static getToken(): string | null {
    return localStorage.getItem('access_token');
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem('refresh_token');
  }

  static getUser(): User | null {
    const userData = localStorage.getItem('user_data');
    return userData ? JSON.parse(userData) : null;
  }

  static isAuthenticated(): boolean {
    return !!this.getToken();
  }

  static hasRole(role: string): boolean {
    const user = this.getUser();
    return user?.user_type === role || false;
  }

  static isAdmin(): boolean {
    return this.hasRole('admin');
  }

  static isCollaborator(): boolean {
    return this.hasRole('collaborator');
  }

  static isUser(): boolean {
    return this.hasRole('user');
  }
}

export default AuthService;
